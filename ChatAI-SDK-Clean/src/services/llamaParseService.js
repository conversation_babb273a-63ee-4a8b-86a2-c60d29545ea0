const fetch = require('node-fetch');
const FormData = require('form-data');

/**
 * LlamaParse Service for document parsing and text extraction
 * Moved from User-Service for better separation of concerns
 */
class LlamaParseService {
  constructor() {
    // Use the working API key
    this.apiKey = process.env.LLAMA_CLOUD_API_KEY || 'llx-TbU8YjpDLXfbJ4lwYwbDJYp5DKllwMIcGfB3SGJwGJ7pvtCp';
    this.baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
    this.isConfigured = !!this.apiKey;

    if (!this.apiKey) {
      console.warn('⚠️  LLAMA_CLOUD_API_KEY not configured. Document parsing will be disabled.');
    } else {
      console.log('✅ LlamaParseService initialized successfully');
    }
  }

  checkConfiguration() {
    if (!this.isConfigured) {
      throw new Error('LlamaParse service is not configured. Please set LLAMA_CLOUD_API_KEY.');
    }
  }

  /**
   * Parse file using LlamaParse API with webhook support
   */
  async parseFile(fileBuffer, filename, jobMetadata = {}) {
    this.checkConfiguration();

    try {
      console.log(`📄 Starting to parse file: ${filename}`);
      console.log(`🔔 Using webhook-based parsing (no polling)`);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', fileBuffer, {
        filename: filename,
        contentType: this.getContentType(filename)
      });

      // Add webhook URL to the form data
      const webhookUrl = this.getWebhookUrl();
      formData.append('webhook_url', webhookUrl);

      console.log(`🔗 Webhook URL: ${webhookUrl}`);

      // Upload file and get job ID
      const uploadResponse = await fetch(`${this.baseUrl}/parsing/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          ...formData.getHeaders()
        },
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        console.error(`❌ LlamaParse upload error: ${uploadResponse.status} - ${errorText}`);
        throw new Error(`Upload failed: ${uploadResponse.status}`);
      }

      const uploadResult = await uploadResponse.json();
      const jobId = uploadResult.id;

      if (!jobId) {
        throw new Error('Failed to get job ID from upload response');
      }

      console.log(`🔄 File uploaded, job ID: ${jobId}`);
      console.log(`🔔 Webhook will be called when parsing is complete`);
      console.log(`⚡ No polling needed - waiting for webhook...`);

      // Return a promise that will be resolved by the webhook
      return new Promise((resolve, reject) => {
        const webhookService = require('./webhookService');

        // Register this job to wait for webhook response
        webhookService.registerPendingJob(jobId, {
          filename,
          ...jobMetadata
        }, resolve, reject);

        console.log(`✅ Job ${jobId} registered for webhook response`);
      });

    } catch (error) {
      console.error(`❌ LlamaParse error for ${filename}:`, error.message);
      throw error;
    }
  }

  /**
   * Get webhook URL for LlamaParse callbacks
   */
  getWebhookUrl() {
    // Get the base URL from environment or use default
    const baseUrl = process.env.WEBHOOK_BASE_URL || 'http://localhost:3001';
    return `${baseUrl}/api/llamaparse-webhook`;
  }

  /**
   * Legacy method - now replaced by webhook approach
   * Kept for backward compatibility but will throw error
   */
  async getParseStatus(jobId) {
    throw new Error('getParseStatus is deprecated - using webhook approach instead');
  }

  /**
   * Legacy method - now replaced by webhook approach
   * Kept for backward compatibility but will throw error
   */
  async getParseResult(jobId) {
    throw new Error('getParseResult is deprecated - using webhook approach instead');
  }



  /**
   * Count words in text
   */
  countWords(text) {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Get content type based on file extension
   */
  getContentType(filename) {
    const ext = filename.toLowerCase().split('.').pop();
    const contentTypes = {
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      txt: 'text/plain',
      md: 'text/markdown',
      html: 'text/html',
      rtf: 'application/rtf',
    };

    return contentTypes[ext] || 'application/octet-stream';
  }

  /**
   * Parse text content directly (for already extracted text)
   */
  async parseTextContent(text, filename = 'text_content.txt') {
    console.log(`📄 Processing text content directly: ${filename} `);

    const wordCount = this.countWords(text);

    return {
      id: `text_${Date.now()} `,
      status: 'SUCCESS',
      result: {
        text: text,
        metadata: {
          page_count: 1,
          word_count: wordCount,
          filename: filename,
        },
      },
    };
  }
}

module.exports = new LlamaParseService();
