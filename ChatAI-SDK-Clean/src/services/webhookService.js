/**
 * Webhook Service for handling LlamaParse webhook responses
 * Manages pending jobs and processes webhook data
 */

class WebhookService {
  constructor() {
    // Store pending jobs waiting for webhook responses
    this.pendingJobs = new Map();

    // Store job timeouts
    this.jobTimeouts = new Map();

    // Default timeout for jobs (5 minutes)
    this.defaultTimeout = 5 * 60 * 1000;
  }

  /**
   * Register a pending job that's waiting for webhook response
   * @param {string} jobId - LlamaParse job ID
   * @param {Object} jobData - Job metadata
   * @param {Function} resolve - Promise resolve function
   * @param {Function} reject - Promise reject function
   */
  registerPendingJob(jobId, jobData, resolve, reject) {
    console.log(`📝 Registering pending job: ${jobId}`);

    this.pendingJobs.set(jobId, {
      ...jobData,
      resolve,
      reject,
      timestamp: Date.now()
    });

    // Set timeout for the job
    const timeout = setTimeout(() => {
      console.error(`⏰ Job ${jobId} timed out after ${this.defaultTimeout}ms`);
      this.rejectJob(jobId, new Error(`Job ${jobId} timed out - no webhook received`));
    }, this.defaultTimeout);

    this.jobTimeouts.set(jobId, timeout);

    console.log(`✅ Job ${jobId} registered, waiting for webhook...`);
  }

  /**
   * Handle incoming LlamaParse webhook
   * @param {Object} webhookData - Webhook payload from LlamaParse
   */
  async handleLlamaParseWebhook(webhookData) {
    const { txt, md, pages, images } = webhookData;

    console.log(`🔔 Processing webhook response`);
    console.log(`📄 Pages: ${pages.length}, Text: ${txt.length} chars`);

    // Since LlamaParse doesn't send job ID in webhook, match with most recent pending job
    // This works because we typically process one document at a time
    const pendingJobEntries = Array.from(this.pendingJobs.entries());

    if (pendingJobEntries.length === 0) {
      console.warn(`⚠️ Received webhook but no pending jobs found`);
      return;
    }

    // Get the most recent pending job (FIFO approach)
    const [jobId, pendingJob] = pendingJobEntries[0];
    console.log(`🎯 Matching webhook with job: ${jobId}`);

    try {
      // Clear the timeout
      const timeout = this.jobTimeouts.get(jobId);
      if (timeout) {
        clearTimeout(timeout);
        this.jobTimeouts.delete(jobId);
      }

      // Process the webhook data into the expected format
      const processedResult = this.processWebhookData({
        jobId,
        txt,
        md,
        pages,
        images
      });

      console.log(`✅ Webhook data processed for job: ${jobId}`);
      console.log(`📄 Pages: ${processedResult.result.pages.length}`);
      console.log(`📊 Total text: ${processedResult.result.text.length} characters`);

      // Resolve the pending job with the processed result
      pendingJob.resolve(processedResult);

      // Clean up
      this.pendingJobs.delete(jobId);

      // Continue with vector processing
      await this.continueVectorProcessing(pendingJob, processedResult);

    } catch (error) {
      console.error(`❌ Error processing webhook for job ${jobId}:`, error.message);
      this.rejectJob(jobId, error);
    }
  }

  /**
   * Process webhook data into the expected LlamaParse result format
   * @param {Object} webhookData - Raw webhook data
   * @returns {Object} Processed result in LlamaParse format
   */
  processWebhookData({ jobId, txt, md, pages, images }) {
    console.log(`🔧 Processing webhook data for job: ${jobId}`);

    // Validate pages data
    if (!pages || !Array.isArray(pages)) {
      throw new Error('Invalid webhook data: missing or invalid pages array');
    }

    const pageCount = pages.length;
    console.log(`📄 Processing ${pageCount} pages`);

    // Process pages into our expected format
    const processedPages = pages.map((page, index) => ({
      pageNumber: page.page || index + 1,
      text: page.text || '',
      markdown: page.md || '',
      images: page.images || [],
      items: page.items || [],
      wordCount: this.countWords(page.text || page.md || '')
    }));

    // Calculate total word count
    const totalWordCount = this.countWords(txt || '');

    return {
      id: jobId,
      status: 'SUCCESS',
      result: {
        // Full text for backward compatibility
        text: txt || '',
        // Page-based data for improved chunking
        pages: processedPages,
        metadata: {
          page_count: pageCount,
          word_count: totalWordCount,
          filename: 'webhook-received', // Will be updated with actual filename
          job_metadata: {
            webhook_received: true,
            images_count: images ? images.length : 0
          }
        },
      },
    };
  }

  /**
   * Continue with vector processing after webhook is received
   * @param {Object} pendingJob - The pending job data
   * @param {Object} parseResult - The processed parse result
   */
  async continueVectorProcessing(pendingJob, parseResult) {
    try {
      console.log('\n🔧 Continuing vector processing after webhook...');

      const { documentId, appId, filename, userId } = pendingJob;

      // Update filename in metadata
      parseResult.result.metadata.filename = filename;

      // Import vector processing function
      const { processDocumentForVector, notifyUserServiceStatus } = require('../routes/vectorProcessing');

      // Update status to embedding
      console.log('\n🔧 Step 2: Starting SambaNova embedding generation');
      console.log('==================================================');
      await notifyUserServiceStatus(appId, documentId, 'embedding', 'SambaNova embedding generation in progress');

      // Update status to indexing
      console.log('\n🔧 Step 3: Starting Qdrant vector indexing');
      console.log('==========================================');
      await notifyUserServiceStatus(appId, documentId, 'indexing', 'Qdrant vector indexing in progress');

      // Process for vector search
      console.log('\n🔧 Step 4: Processing for vector search');
      console.log('=======================================');

      const vectorResult = await processDocumentForVector({
        documentId,
        appId,
        filename,
        userId,
        parsedText: parseResult.result.text,
        pages: parseResult.result.pages,
        metadata: parseResult.result.metadata
      });

      console.log(`✅ Complete processing finished`);

      // Update final status to ready
      console.log('\n🔧 Step 5: Updating final status to ready');
      console.log('=========================================');

      await notifyUserServiceStatus(appId, documentId, 'ready', 'Document ready for semantic search', {
        parsedData: `Processed by ChatAI-SDK-Clean - ${parseResult.result.text.length} characters`,
        pageCount: parseResult.result.metadata.page_count,
        wordCount: parseResult.result.metadata.word_count,
        indexId: documentId
      });

      console.log(`🎉 Document processing complete: ${documentId}`);

    } catch (error) {
      console.error('❌ Vector processing error after webhook:', error.message);

      // Update status to error
      if (pendingJob.appId && pendingJob.documentId) {
        const { notifyUserServiceStatus } = require('../routes/vectorProcessing');
        await notifyUserServiceStatus(pendingJob.appId, pendingJob.documentId, 'error', `Processing failed: ${error.message}`);
      }
    }
  }

  /**
   * Reject a pending job with an error
   * @param {string} jobId - Job ID to reject
   * @param {Error} error - Error to reject with
   */
  rejectJob(jobId, error) {
    const pendingJob = this.pendingJobs.get(jobId);
    if (pendingJob) {
      pendingJob.reject(error);
      this.pendingJobs.delete(jobId);
    }

    // Clear timeout
    const timeout = this.jobTimeouts.get(jobId);
    if (timeout) {
      clearTimeout(timeout);
      this.jobTimeouts.delete(jobId);
    }
  }

  /**
   * Count words in text
   * @param {string} text - Text to count words in
   * @returns {number} Word count
   */
  countWords(text) {
    if (!text || typeof text !== 'string') return 0;
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Get pending jobs count (for monitoring)
   * @returns {number} Number of pending jobs
   */
  getPendingJobsCount() {
    return this.pendingJobs.size;
  }

  /**
   * Clean up expired jobs (called periodically)
   */
  cleanupExpiredJobs() {
    const now = Date.now();
    const expiredJobs = [];

    for (const [jobId, job] of this.pendingJobs.entries()) {
      if (now - job.timestamp > this.defaultTimeout) {
        expiredJobs.push(jobId);
      }
    }

    expiredJobs.forEach(jobId => {
      console.warn(`🧹 Cleaning up expired job: ${jobId}`);
      this.rejectJob(jobId, new Error(`Job ${jobId} expired`));
    });

    if (expiredJobs.length > 0) {
      console.log(`🧹 Cleaned up ${expiredJobs.length} expired jobs`);
    }
  }
}

module.exports = new WebhookService();
