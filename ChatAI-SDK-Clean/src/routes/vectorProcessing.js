const express = require('express');
const multer = require('multer');
const router = express.Router();
const embeddingService = require('../services/embeddingService');
const qdrantService = require('../services/qdrantService');
const llamaParseService = require('../services/llamaParseService');
const fetch = require('node-fetch');

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow common document types
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/markdown',
      'text/html',
      'application/rtf'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Unsupported file type: ${file.mimetype}`), false);
    }
  }
});

/**
 * Complete document upload and processing
 * Handles file upload, LlamaIndex parsing, and vector storage
 */
router.post('/upload-and-process', upload.single('file'), async (req, res) => {
  try {
    console.log('\n🔧 Complete Document Upload and Processing');
    console.log('==========================================');

    const { appId, documentId, userId, filename } = req.body;
    const file = req.file;

    // Validate required fields
    if (!appId || !documentId || !file) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: appId, documentId, file'
      });
    }

    const actualFilename = filename || file.originalname;
    console.log(`📄 File: ${actualFilename}`);
    console.log(`🏢 AppId: ${appId}`);
    console.log(`📊 File size: ${file.size} bytes`);

    // Step 1: Update status to parsing and parse document
    console.log('\n🔧 Step 1: Parsing document with LlamaIndex');
    console.log('============================================');

    await notifyUserServiceStatus(appId, documentId, 'parsing', 'LlamaIndex parsing in progress');

    const parseResult = await llamaParseService.parseFile(file.buffer, actualFilename, {
      documentId,
      appId,
      userId
    });

    if (parseResult.status !== 'SUCCESS' || !parseResult.result) {
      await notifyUserServiceStatus(appId, documentId, 'error', 'Document parsing failed');
      throw new Error('Document parsing failed');
    }

    console.log(`✅ Document parsed successfully`);
    console.log(`📊 Text length: ${parseResult.result.text.length} characters`);
    console.log(`📄 Pages: ${parseResult.result.metadata.page_count}`);
    console.log(`📝 Words: ${parseResult.result.metadata.word_count}`);

    // Step 2: Update status to embedding and start vector processing
    console.log('\n🔧 Step 2: Starting SambaNova embedding generation');
    console.log('==================================================');
    await notifyUserServiceStatus(appId, documentId, 'embedding', 'SambaNova embedding generation in progress');

    // Step 3: Update status to indexing before Qdrant storage
    console.log('\n🔧 Step 3: Starting Qdrant vector indexing');
    console.log('==========================================');
    await notifyUserServiceStatus(appId, documentId, 'indexing', 'Qdrant vector indexing in progress');

    // Step 4: Process for vector search
    console.log('\n🔧 Step 4: Processing for vector search');
    console.log('=======================================');

    const vectorResult = await processDocumentForVector({
      documentId,
      appId,
      filename: actualFilename,
      userId,
      parsedText: parseResult.result.text,
      pages: parseResult.result.pages, // Pass page-based data
      metadata: parseResult.result.metadata
    });

    console.log(`✅ Complete processing finished`);

    // Step 5: Update final status to ready
    console.log('\n🔧 Step 5: Updating final status to ready');
    console.log('=========================================');

    await notifyUserServiceStatus(appId, documentId, 'ready', 'Document ready for semantic search', {
      parsedData: `Processed by ChatAI-SDK-Clean - ${parseResult.result.text.length} characters`,
      pageCount: parseResult.result.metadata.page_count,
      wordCount: parseResult.result.metadata.word_count,
      indexId: documentId
    });

    console.log(`🎉 Document processing complete: ${documentId}`);

    // Return comprehensive result
    res.json({
      success: true,
      message: 'Document uploaded, parsed, and processed for vector search',
      data: {
        documentId,
        appId,
        filename: actualFilename,
        parsing: {
          jobId: parseResult.id,
          textLength: parseResult.result.text.length,
          pageCount: parseResult.result.metadata.page_count,
          wordCount: parseResult.result.metadata.word_count
        },
        vectorProcessing: {
          totalChunks: vectorResult.totalChunks,
          storedChunks: vectorResult.storedChunks,
          chunkingStrategy: vectorResult.chunkingStrategy,
          pageBasedChunks: vectorResult.pageBasedChunks,
          status: 'ready_for_chat'
        },
        status: 'ready_for_chat'
      }
    });

  } catch (error) {
    console.error('❌ Complete document processing error:', error);

    // Update status to error
    if (appId && documentId) {
      await notifyUserServiceStatus(appId, documentId, 'error', `Processing failed: ${error.message}`);
    }

    res.status(500).json({
      success: false,
      error: 'Complete document processing failed',
      message: error.message
    });
  }
});

/**
 * Process document for vector search
 * Called by User-Service after LlamaIndex parsing is complete
 */
router.post('/process-document', async (req, res) => {
  try {
    console.log('\n🔧 Processing document for vector search');
    console.log('========================================');

    const {
      documentId,
      appId,
      filename,
      userId,
      parsedText,
      metadata = {}
    } = req.body;

    // Validate required fields
    if (!documentId || !appId || !parsedText) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: documentId, appId, parsedText'
      });
    }

    const result = await processDocumentForVector({
      documentId,
      appId,
      filename,
      userId,
      parsedText,
      metadata
    });

    // Return success response
    res.json({
      success: true,
      message: 'Document processed for vector search',
      data: {
        documentId,
        appId,
        totalChunks: result.totalChunks,
        storedChunks: result.storedChunks,
        chunks: result.chunks,
        status: 'ready_for_chat'
      }
    });

  } catch (error) {
    console.error('❌ Vector processing error:', error);
    res.status(500).json({
      success: false,
      error: 'Vector processing failed',
      message: error.message
    });
  }
});

/**
 * Helper function to process document for vector search
 */
async function processDocumentForVector({ documentId, appId, filename, userId, parsedText, pages = null, metadata = {} }) {
  console.log(`📄 Document: ${filename || documentId}`);
  console.log(`🏢 AppId: ${appId}`);
  console.log(`📊 Text length: ${parsedText.length} characters`);

  // Initialize Qdrant if not already done
  await qdrantService.initialize();

  // Step 1: Determine chunking strategy
  let chunks = [];
  let chunkMetadata = [];

  if (pages && Array.isArray(pages) && pages.length > 0) {
    // Use page-based chunking (natural document boundaries)
    console.log('\n🔧 Step 1: Using page-based chunking (natural boundaries)');
    console.log(`📄 Processing ${pages.length} pages as natural chunks`);

    chunks = pages.map(page => page.text || page.markdown || '').filter(text => text.trim());
    chunkMetadata = pages.map((page, index) => ({
      chunkType: 'page',
      pageNumber: page.pageNumber || index + 1,
      wordCount: page.wordCount || 0,
      hasImages: (page.images && page.images.length > 0),
      imageCount: page.images ? page.images.length : 0,
      hasStructuredItems: (page.items && page.items.length > 0)
    }));

    console.log(`✅ Created ${chunks.length} page-based chunks`);
    pages.forEach((page, index) => {
      console.log(`   📄 Page ${page.pageNumber || index + 1}: ${chunks[index].length} chars, ${page.wordCount || 0} words, ${page.images?.length || 0} images`);
    });
  } else {
    // Fallback to artificial chunking for backward compatibility
    console.log('\n🔧 Step 1: Using artificial chunking (fallback)');
    console.log('⚠️  No page data available, falling back to text splitting');

    chunks = embeddingService.splitTextIntoChunks(parsedText, 1000, 100);
    chunkMetadata = chunks.map((chunk, index) => ({
      chunkType: 'artificial',
      chunkIndex: index,
      wordCount: embeddingService.countWords ? embeddingService.countWords(chunk) : chunk.split(/\s+/).length
    }));

    console.log(`📄 Split into ${chunks.length} artificial chunks`);
  }

  // Step 2: Generate embeddings for all chunks using batch processing
  console.log('\n🚀 Step 2: Generating embeddings (batch processing)');
  // Let the embedding service determine optimal batch size based on provider and rate limits
  const allEmbeddings = await embeddingService.generateBatchEmbeddings(chunks);
  console.log(`✅ Generated embeddings for all ${chunks.length} chunks`);

  // Step 3: Store all chunks in Qdrant (handle partial failures)
  console.log('\n💾 Step 3: Storing chunks in Qdrant');
  const storedChunks = [];
  const failedChunks = [];

  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const embeddings = allEmbeddings[i];

    // Skip chunks with null embeddings (failed batches)
    if (!embeddings) {
      console.warn(`⚠️ Skipping chunk ${i + 1} - no embeddings available (batch failed)`);
      failedChunks.push({
        chunkIndex: i,
        reason: 'No embeddings - batch processing failed'
      });
      continue;
    }

    try {
      // Store in Qdrant with enhanced metadata
      const enhancedMetadata = {
        appId,
        documentId,
        filename: filename || documentId,
        userId,
        chunkIndex: i,
        totalChunks: chunks.length,
        ...metadata,
        ...chunkMetadata[i] // Add page-specific or chunk-specific metadata
      };

      const pointId = await qdrantService.storeDocument({
        text: chunk,
        metadata: enhancedMetadata
      }, embeddings);

      storedChunks.push({
        chunkIndex: i,
        pointId: pointId,
        textLength: chunk.length,
        chunkType: chunkMetadata[i].chunkType,
        pageNumber: chunkMetadata[i].pageNumber || null
      });

      if ((i + 1) % 10 === 0 || i === chunks.length - 1) {
        const chunkType = chunkMetadata[0].chunkType;
        console.log(`✅ Stored ${storedChunks.length}/${chunks.length} ${chunkType} chunks in Qdrant`);
      }

    } catch (chunkError) {
      console.error(`❌ Failed to store chunk ${i + 1}:`, chunkError.message);
      failedChunks.push({
        chunkIndex: i,
        reason: `Storage failed: ${chunkError.message}`
      });
      // Continue with other chunks
    }
  }

  // Report storage results
  console.log(`\n📊 Storage Summary:`);
  console.log(`✅ Successfully stored: ${storedChunks.length}/${chunks.length} chunks`);
  console.log(`❌ Failed to store: ${failedChunks.length}/${chunks.length} chunks`);

  if (failedChunks.length > 0) {
    console.log(`\n💥 Failed chunk details:`);
    failedChunks.forEach(failed => {
      console.log(`   Chunk ${failed.chunkIndex + 1}: ${failed.reason}`);
    });
  }

  const chunkingStrategy = chunkMetadata[0]?.chunkType || 'unknown';
  const pageBasedChunks = chunkingStrategy === 'page' ? chunks.length : 0;

  console.log(`\n✅ Document processing complete`);
  console.log(`📊 Chunking strategy: ${chunkingStrategy}`);
  if (pageBasedChunks > 0) {
    console.log(`📄 Page-based chunks: ${pageBasedChunks}`);
  }

  // Determine overall success status
  const successRate = storedChunks.length / chunks.length;
  const isFullSuccess = successRate === 1.0;
  const isPartialSuccess = successRate >= 0.8; // 80% threshold for partial success

  let status, message;
  if (isFullSuccess) {
    status = 'success';
    message = `Document processed successfully - all ${chunks.length} ${chunkingStrategy} chunks stored`;
  } else if (isPartialSuccess) {
    status = 'partial_success';
    message = `Document processed with ${Math.round(successRate * 100)}% success rate - ${storedChunks.length}/${chunks.length} ${chunkingStrategy} chunks stored`;
  } else {
    status = 'failed';
    message = `Document processing failed - only ${storedChunks.length}/${chunks.length} ${chunkingStrategy} chunks stored (${Math.round(successRate * 100)}%)`;
  }

  console.log(`📊 Final Status: ${status.toUpperCase()}`);
  console.log(`📊 ${message}`);

  return {
    status,
    message,
    totalChunks: chunks.length,
    storedChunks: storedChunks.length,
    failedChunks: failedChunks.length,
    chunkingStrategy: chunkingStrategy,
    pageBasedChunks: pageBasedChunks,
    successRate: Math.round(successRate * 100),
    chunks: storedChunks,
    failedChunkDetails: failedChunks,
    success: isPartialSuccess // Consider partial success as success for backward compatibility
  };
}

/**
 * Delete document from vector database
 * Called when document is deleted from User-Service
 * Requires internal API key for security
 */
router.delete('/document/:documentId', async (req, res) => {
  try {
    const { documentId } = req.params;
    const { appId } = req.query;

    // Verify internal API key for security
    const expectedKey = process.env.INTERNAL_API_KEY || 'chatai-internal-2024';
    const internalApiKey = req.headers['x-internal-api-key'];

    if (internalApiKey !== expectedKey) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Invalid internal API key'
      });
    }

    if (!documentId || !appId) {
      return res.status(400).json({
        success: false,
        error: 'Missing documentId or appId'
      });
    }

    console.log(`🗑️ Deleting document ${documentId} from vector database for app ${appId}`);

    await qdrantService.initialize();

    // Delete all chunks for this document
    const deletedCount = await qdrantService.deleteDocument(documentId, appId);

    console.log(`✅ Deleted ${deletedCount} chunks for document ${documentId}`);

    res.json({
      success: true,
      message: 'Document deleted from vector database',
      data: {
        documentId,
        appId,
        deletedChunks: deletedCount
      }
    });

  } catch (error) {
    console.error('❌ Vector deletion error:', error);
    res.status(500).json({
      success: false,
      error: 'Vector deletion failed',
      message: error.message
    });
  }
});

/**
 * Get vector database stats for an app
 */
router.get('/stats/:appId', async (req, res) => {
  try {
    const { appId } = req.params;

    await qdrantService.initialize();

    const stats = await qdrantService.getAppStats(appId);

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Vector stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get vector stats',
      message: error.message
    });
  }
});

/**
 * Health check for vector processing
 */
router.get('/health', async (req, res) => {
  try {
    await qdrantService.initialize();

    const embeddingInfo = embeddingService.getProviderInfo();

    const health = {
      status: 'healthy',
      qdrant: qdrantService.isInitialized ? 'connected' : 'disconnected',
      embedding: {
        provider: embeddingInfo.provider,
        model: embeddingInfo.model,
        dimensions: embeddingInfo.dimensions,
        configured: embeddingInfo.isConfigured
      },
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: health
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Vector processing unhealthy',
      message: error.message
    });
  }
});

/**
 * Notify User-Service about status updates via internal API
 * @param {string} appId - Application ID
 * @param {string} documentId - Document ID
 * @param {string} status - New status (parsing, embedding, indexing, ready, error)
 * @param {string} message - Status message
 * @param {Object} additionalData - Additional data (parsedData, pageCount, etc.)
 */
async function notifyUserServiceStatus(appId, documentId, status, message, additionalData = {}) {
  try {
    const userServiceUrl = process.env.USER_SERVICE_URL || 'http://localhost:3000';
    const internalApiKey = process.env.INTERNAL_API_KEY || 'chatai-internal-2024';

    console.log(`📡 Updating User-Service: Document ${documentId} → ${status}`);
    console.log(`💬 Message: ${message}`);

    const updateData = {
      appId,
      documentId,
      status,
      message,
      ...additionalData
    };

    const response = await fetch(`${userServiceUrl}/users/app/chatai/internal/update-document-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-internal-api-key': internalApiKey
      },
      body: JSON.stringify(updateData)
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`✅ User-Service status updated: ${status}`);
      return result;
    } else {
      const errorText = await response.text();
      console.warn(`⚠️ Failed to update User-Service: ${response.status} - ${errorText}`);
      return null;
    }

  } catch (error) {
    console.warn(`⚠️ Status notification failed: ${error.message}`);
    // Don't throw error - status notification is optional
    return null;
  }
}

// Export functions for webhook service
module.exports = router;
module.exports.processDocumentForVector = processDocumentForVector;
module.exports.notifyUserServiceStatus = notifyUserServiceStatus;
